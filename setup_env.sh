#!/bin/bash

# 设置工作目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

echo "开始设置Python环境..."
echo "工作目录: $SCRIPT_DIR"

# 检查Python3是否可用
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到python3命令"
    exit 1
fi

echo "Python版本: $(python3 --version)"

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "创建Python虚拟环境..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "错误: 创建虚拟环境失败"
        exit 1
    fi
else
    echo "虚拟环境已存在，跳过创建"
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 升级pip
echo "升级pip..."
pip install --upgrade pip

# 安装依赖
echo "安装依赖包..."
if [ -f "requirements.txt" ]; then
    echo "从requirements.txt安装依赖..."
    pip install -r requirements.txt
else
    echo "从命令行安装依赖..."
    pip install requests python-dotenv
fi

if [ $? -ne 0 ]; then
    echo "错误: 依赖安装失败"
    deactivate
    exit 1
fi

# 验证依赖安装
echo "验证依赖安装..."
python -c "import requests, dotenv; print('依赖验证成功')"
if [ $? -ne 0 ]; then
    echo "错误: 依赖验证失败"
    deactivate
    exit 1
fi

# 退出虚拟环境
deactivate

# 设置执行权限
chmod +x run_collector.sh

echo "环境设置完成！"