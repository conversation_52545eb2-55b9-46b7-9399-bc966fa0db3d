#!/usr/bin/env python3
"""
测试upsert功能的脚本
"""

import os
import sys
import time
import json
from dotenv import load_dotenv
from directus_client import DirectusClient
import logging

# 加载环境变量
load_dotenv()

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_upsert_functionality():
    """测试upsert功能"""
    logger = setup_logging()
    
    # 获取配置
    directus_api_url = os.getenv('DIRECTUS_API_URL')
    directus_token = os.getenv('DIRECTUS_TOKEN')
    
    if not directus_api_url or not directus_token:
        logger.error("缺少Directus配置信息")
        return False
    
    logger.info(f"测试Directus upsert功能: {directus_api_url}")
    
    try:
        # 创建Directus客户端
        client = DirectusClient(
            api_url=directus_api_url,
            token=directus_token,
            logger=logger
        )
        
        # 生成测试订单号
        test_order_no = "TEST_UPSERT_" + str(int(time.time()))
        logger.info(f"使用测试订单号: {test_order_no}")
        
        # 测试数据
        test_data = {
            'orderNo': test_order_no,
            'outStatus': '测试状态',
            'businessStatus': '测试业务状态',
            'createTime': '2025-01-01T00:00:00',
            'updateTime': '2025-01-01T00:00:00',
            'target': '***********',
            'send_goods_result': json.dumps({"test": "data"}, ensure_ascii=False),
            'send_goods_result_Alias': '测试描述',
            'logisticsStatus': '未发货',
            'channel_config': 5,  # 使用一个存在的渠道配置ID
            'Phone_Card': 'BJYD_58Y_SFQSK'  # 使用一个有效的SKU代码
        }
        
        # 测试1: 创建新订单
        logger.info("测试1: 创建新订单...")
        result1, is_update1 = client.upsert_item(
            collection='forders',
            primary_key=test_order_no,
            data=test_data
        )
        
        if not is_update1:
            logger.info(f"✅ 新订单创建成功: {result1['orderNo']}")
        else:
            logger.error("❌ 预期创建新订单，但返回了更新操作")
            return False
        
        # 测试2: 更新现有订单
        logger.info("测试2: 更新现有订单...")
        updated_data = test_data.copy()
        updated_data['businessStatus'] = '更新后的业务状态'
        updated_data['updateTime'] = '2025-01-01T12:00:00'
        updated_data['outStatus'] = '已激活'
        
        result2, is_update2 = client.upsert_item(
            collection='forders',
            primary_key=test_order_no,
            data=updated_data
        )
        
        if is_update2:
            logger.info(f"✅ 订单更新成功: {result2['orderNo']}")
            logger.info(f"业务状态已更新为: {result2['businessStatus']}")
        else:
            logger.error("❌ 预期更新现有订单，但返回了创建操作")
            return False
        
        # 验证更新结果
        logger.info("验证更新结果...")
        final_order = client.get_item('forders', test_order_no)
        if final_order and final_order['businessStatus'] == '更新后的业务状态':
            logger.info("✅ 更新验证成功")
        else:
            logger.error("❌ 更新验证失败")
            return False
        
        # 清理测试数据
        logger.info("清理测试数据...")
        if client.delete_item('forders', test_order_no):
            logger.info("✅ 测试数据清理成功")
        else:
            logger.warning("⚠️ 测试数据清理失败")
        
        logger.info("🎉 upsert功能测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_upsert_functionality()
    sys.exit(0 if success else 1)
