#!/usr/bin/env python3
"""
测试修复后的upsert功能
"""

import os
import sys
import time
import json
from dotenv import load_dotenv
from directus_client import DirectusClient
import logging

# 加载环境变量
load_dotenv()

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_fixed_upsert():
    """测试修复后的upsert功能"""
    logger = setup_logging()
    
    # 获取配置
    directus_api_url = os.getenv('DIRECTUS_API_URL')
    directus_token = os.getenv('DIRECTUS_TOKEN')
    
    if not directus_api_url or not directus_token:
        logger.error("缺少Directus配置信息")
        return False
    
    logger.info("开始测试修复后的upsert功能...")
    
    try:
        # 创建Directus客户端
        client = DirectusClient(
            api_url=directus_api_url,
            token=directus_token,
            logger=logger
        )
        
        # 生成测试数据
        test_count = 5
        base_time = int(time.time())
        test_orders = []
        
        for i in range(test_count):
            order_no = f'FIXED_TEST_{base_time}_{i:03d}'
            order_data = {
                'orderNo': order_no,
                'outStatus': '已激活',
                'businessStatus': '交易成功',
                'createTime': '2025-01-01T00:00:00',
                'updateTime': '2025-01-01T00:00:00',
                'target': f'138{i:08d}',
                'send_goods_result': json.dumps({"test": f"fixed_data_{i}", "version": 1}, ensure_ascii=False),
                'send_goods_result_Alias': f'修复测试描述_{i}_v1',
                'logisticsStatus': '发货成功',
                'channel_config': 5,
                'Phone_Card': 'BJYD_58Y_SFQSK'
            }
            test_orders.append(order_data)
        
        logger.info(f"生成测试数据: {len(test_orders)} 条")
        
        # 第一次批量upsert（应该全部创建）
        logger.info("第一次批量upsert（创建）...")
        start_time = time.time()
        
        result1 = client.batch_upsert_items(
            collection='forders',
            items=test_orders,
            primary_key_field='orderNo'
        )
        
        time1 = time.time() - start_time
        
        logger.info(f"第一次upsert结果:")
        logger.info(f"  创建: {result1['created']} 条")
        logger.info(f"  更新: {result1['updated']} 条")
        logger.info(f"  失败: {result1['failed']} 条")
        logger.info(f"  耗时: {time1:.2f}秒")
        
        # 修改数据进行第二次upsert（应该全部更新）
        logger.info("修改数据进行第二次upsert（更新）...")
        for i, order in enumerate(test_orders):
            order['send_goods_result'] = json.dumps({"test": f"fixed_data_{i}", "version": 2}, ensure_ascii=False)
            order['send_goods_result_Alias'] = f'修复测试描述_{i}_v2'
            order['updateTime'] = '2025-01-01T12:00:00'
        
        start_time = time.time()
        
        result2 = client.batch_upsert_items(
            collection='forders',
            items=test_orders,
            primary_key_field='orderNo'
        )
        
        time2 = time.time() - start_time
        
        logger.info(f"第二次upsert结果:")
        logger.info(f"  创建: {result2['created']} 条")
        logger.info(f"  更新: {result2['updated']} 条")
        logger.info(f"  失败: {result2['failed']} 条")
        logger.info(f"  耗时: {time2:.2f}秒")
        
        # 验证数据是否正确更新
        logger.info("验证数据更新...")
        for order in test_orders:
            order_no = order['orderNo']
            try:
                # 查询更新后的数据
                updated_order = client.get_item('forders', order_no, ['orderNo', 'send_goods_result_Alias', 'updateTime'])
                if updated_order:
                    if 'v2' in updated_order.get('send_goods_result_Alias', ''):
                        logger.info(f"✅ 订单 {order_no} 更新验证成功")
                    else:
                        logger.error(f"❌ 订单 {order_no} 更新验证失败")
                else:
                    logger.error(f"❌ 订单 {order_no} 查询失败")
            except Exception as e:
                logger.error(f"❌ 验证订单 {order_no} 时出错: {str(e)}")
        
        # 清理测试数据
        logger.info("清理测试数据...")
        cleanup_start = time.time()
        for order in test_orders:
            try:
                client.delete_item('forders', order['orderNo'])
            except Exception as e:
                logger.warning(f"删除失败: {order['orderNo']} - {str(e)}")
        
        cleanup_time = time.time() - cleanup_start
        logger.info(f"清理完成，耗时: {cleanup_time:.2f}秒")
        
        # 测试总结
        logger.info(f"\n{'='*50}")
        logger.info("修复后的upsert功能测试总结:")
        logger.info(f"测试数据量: {test_count} 条")
        logger.info(f"第一次upsert（创建）: {result1['created']}创建, {result1['updated']}更新, {result1['failed']}失败")
        logger.info(f"第二次upsert（更新）: {result2['created']}创建, {result2['updated']}更新, {result2['failed']}失败")
        logger.info(f"预期结果: 第一次全部创建，第二次全部更新")
        
        # 判断测试是否成功
        success = (
            result1['created'] == test_count and result1['updated'] == 0 and result1['failed'] == 0 and
            result2['created'] == 0 and result2['updated'] == test_count and result2['failed'] == 0
        )
        
        if success:
            logger.info("🎉 测试完全成功！upsert功能工作正常")
        else:
            logger.error("❌ 测试未完全成功，请检查upsert逻辑")
        
        logger.info(f"{'='*50}")
        
        return success
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = test_fixed_upsert()
    sys.exit(0 if success else 1)
