#!/usr/bin/env python3
"""
测试当前版本的directus_client是否被正确加载
"""

import os
import sys
from dotenv import load_dotenv
from directus_client import DirectusClient
import logging
import inspect

# 加载环境变量
load_dotenv()

def test_current_version():
    """测试当前版本"""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    logger.info("测试当前DirectusClient版本...")
    
    # 获取配置
    directus_api_url = os.getenv('DIRECTUS_API_URL')
    directus_token = os.getenv('DIRECTUS_TOKEN')
    
    if not directus_api_url or not directus_token:
        logger.error("缺少Directus配置信息")
        return False
    
    try:
        # 创建客户端
        client = DirectusClient(
            api_url=directus_api_url,
            token=directus_token,
            logger=logger
        )
        
        # 检查batch_upsert_items方法的源码
        method_source = inspect.getsource(client.batch_upsert_items)
        
        if "真正的upsert版本" in method_source:
            logger.info("✅ 使用的是修复后的版本")
            logger.info("✅ batch_upsert_items方法已更新")
            
            # 测试一个简单的upsert操作
            test_data = [{
                'orderNo': 'VERSION_TEST_001',
                'outStatus': '测试状态',
                'businessStatus': '测试业务状态',
                'createTime': '2025-01-01T00:00:00',
                'updateTime': '2025-01-01T00:00:00',
                'target': '***********',
                'send_goods_result': '{"test": "version_check"}',
                'send_goods_result_Alias': '版本测试',
                'logisticsStatus': '未发货',
                'channel_config': 5,
                'Phone_Card': 'BJYD_58Y_SFQSK'
            }]
            
            logger.info("执行测试upsert...")
            result = client.batch_upsert_items(
                collection='forders',
                items=test_data,
                primary_key_field='orderNo'
            )
            
            logger.info(f"测试结果: 创建{result['created']}, 更新{result['updated']}, 失败{result['failed']}")
            
            # 清理测试数据
            client.delete_item('forders', 'VERSION_TEST_001')
            
            if result['failed'] == 0:
                logger.info("🎉 新版本工作正常！")
                return True
            else:
                logger.error("❌ 新版本仍有问题")
                return False
            
        else:
            logger.error("❌ 使用的是旧版本！")
            logger.error("请确保directus_client.py文件已正确保存")
            return False
            
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_current_version()
    sys.exit(0 if success else 1)
