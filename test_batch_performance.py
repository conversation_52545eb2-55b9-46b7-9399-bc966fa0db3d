#!/usr/bin/env python3
"""
批量性能测试脚本 - 测试批量upsert vs 单条upsert的性能差异
"""

import os
import sys
import time
import json
from dotenv import load_dotenv
from directus_client import DirectusClient
import logging

# 加载环境变量
load_dotenv()

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def generate_test_data(count: int) -> list:
    """生成测试数据"""
    test_data = []
    base_time = int(time.time())
    
    for i in range(count):
        order_data = {
            'orderNo': f'BATCH_TEST_{base_time}_{i:04d}',
            'outStatus': '已激活',
            'businessStatus': '交易成功',
            'createTime': '2025-01-01T00:00:00',
            'updateTime': '2025-01-01T00:00:00',
            'target': f'138{i:08d}',
            'send_goods_result': json.dumps({"test": f"batch_data_{i}"}, ensure_ascii=False),
            'send_goods_result_Alias': f'批量测试描述_{i}',
            'logisticsStatus': '发货成功',
            'channel_config': 5,
            'Phone_Card': 'BJYD_58Y_SFQSK'
        }
        test_data.append(order_data)
    
    return test_data

def test_single_upsert_performance(client: DirectusClient, test_data: list) -> dict:
    """测试单条upsert性能"""
    logger = logging.getLogger(__name__)
    logger.info(f"开始单条upsert性能测试，数据量: {len(test_data)} 条")
    
    start_time = time.time()
    created_count = 0
    updated_count = 0
    failed_count = 0
    
    for i, order_data in enumerate(test_data):
        try:
            result, is_update = client.upsert_item(
                collection='forders',
                primary_key=order_data['orderNo'],
                data=order_data
            )
            
            if is_update:
                updated_count += 1
            else:
                created_count += 1
                
            if (i + 1) % 10 == 0:
                logger.info(f"单条处理进度: {i + 1}/{len(test_data)}")
                
        except Exception as e:
            failed_count += 1
            logger.error(f"单条upsert失败: {str(e)}")
    
    total_time = time.time() - start_time
    
    return {
        'method': '单条upsert',
        'total_time': total_time,
        'created': created_count,
        'updated': updated_count,
        'failed': failed_count,
        'avg_time_per_item': total_time / len(test_data),
        'items_per_second': len(test_data) / total_time
    }

def test_batch_upsert_performance(client: DirectusClient, test_data: list) -> dict:
    """测试批量upsert性能"""
    logger = logging.getLogger(__name__)
    logger.info(f"开始批量upsert性能测试，数据量: {len(test_data)} 条")
    
    start_time = time.time()
    
    result = client.batch_upsert_items(
        collection='forders',
        items=test_data,
        primary_key_field='orderNo'
    )
    
    total_time = time.time() - start_time
    
    return {
        'method': '批量upsert',
        'total_time': total_time,
        'created': result['created'],
        'updated': result['updated'],
        'failed': result['failed'],
        'avg_time_per_item': total_time / len(test_data),
        'items_per_second': len(test_data) / total_time
    }

def cleanup_test_data(client: DirectusClient, test_data: list):
    """清理测试数据"""
    logger = logging.getLogger(__name__)
    logger.info(f"清理测试数据，共 {len(test_data)} 条")
    
    cleanup_start = time.time()
    for order_data in test_data:
        try:
            client.delete_item('forders', order_data['orderNo'])
        except Exception as e:
            logger.warning(f"删除测试数据失败: {order_data['orderNo']} - {str(e)}")
    
    cleanup_time = time.time() - cleanup_start
    logger.info(f"清理完成，耗时: {cleanup_time:.2f}秒")

def main():
    logger = setup_logging()
    
    # 获取配置
    directus_api_url = os.getenv('DIRECTUS_API_URL')
    directus_token = os.getenv('DIRECTUS_TOKEN')
    
    if not directus_api_url or not directus_token:
        logger.error("缺少Directus配置信息")
        return False
    
    logger.info("开始批量性能对比测试...")
    
    try:
        # 创建Directus客户端
        client = DirectusClient(
            api_url=directus_api_url,
            token=directus_token,
            logger=logger
        )
        
        # 测试不同数据量
        test_sizes = [50, 100, 200]
        
        for size in test_sizes:
            logger.info(f"\n{'='*50}")
            logger.info(f"测试数据量: {size} 条")
            logger.info(f"{'='*50}")
            
            # 生成测试数据
            test_data = generate_test_data(size)
            
            # 测试单条upsert
            single_result = test_single_upsert_performance(client, test_data)
            
            # 清理数据
            cleanup_test_data(client, test_data)
            
            # 重新生成数据（确保测试条件一致）
            test_data = generate_test_data(size)
            
            # 测试批量upsert
            batch_result = test_batch_upsert_performance(client, test_data)
            
            # 清理数据
            cleanup_test_data(client, test_data)
            
            # 输出对比结果
            logger.info(f"\n性能对比结果 (数据量: {size} 条):")
            logger.info(f"{'方法':<12} {'总耗时(秒)':<12} {'平均耗时(秒)':<15} {'处理速度(条/秒)':<18} {'成功率':<10}")
            logger.info(f"{'-'*70}")
            
            for result in [single_result, batch_result]:
                success_rate = (result['created'] + result['updated']) / size * 100
                logger.info(f"{result['method']:<12} {result['total_time']:<12.2f} {result['avg_time_per_item']:<15.4f} {result['items_per_second']:<18.2f} {success_rate:<10.1f}%")
            
            # 计算性能提升
            if single_result['total_time'] > 0:
                speedup = single_result['total_time'] / batch_result['total_time']
                logger.info(f"\n批量处理性能提升: {speedup:.2f}x")
                logger.info(f"时间节省: {(single_result['total_time'] - batch_result['total_time']):.2f}秒 ({((single_result['total_time'] - batch_result['total_time']) / single_result['total_time'] * 100):.1f}%)")
        
        logger.info(f"\n{'='*50}")
        logger.info("批量性能测试完成！")
        logger.info(f"{'='*50}")
        
        return True
        
    except Exception as e:
        logger.error(f"性能测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
