# 数据采集系统Directus迁移总结

## 概述
本次修改将原有的直接MySQL数据库操作改为使用Directus API，解决了远程MySQL服务器权限问题。

## 主要修改内容

### 1. 新增文件

#### `directus_client.py`
- 新建的Directus API客户端类
- 提供完整的CRUD操作接口
- 支持upsert操作（插入或更新）
- 包含错误处理和日志记录

#### `requirements.txt`
- 新建依赖管理文件
- 移除了pymysql依赖
- 保留requests和python-dotenv

#### 测试文件
- `test_directus_connection.py` - Directus连接测试
- `test_upsert.py` - upsert功能测试
- `test_collector_directus.py` - 采集器集成测试

### 2. 修改的文件

#### `fengzs_collector.py`
**主要变更：**
- 移除了`import pymysql`
- 添加了`from directus_client import DirectusClient`
- 移除了数据库配置`self.db_config`
- 添加了Directus客户端初始化
- 移除了`get_db_connection()`方法
- 重写了`upsert_order()`方法，使用Directus API替代SQL操作

**upsert_order方法变更：**
```python
# 原来的SQL操作
with self.get_db_connection() as conn:
    with conn.cursor() as cursor:
        cursor.execute(sql, data)
        conn.commit()

# 现在的Directus API操作
result_data, is_update = self.directus_client.upsert_item(
    collection='forders',
    primary_key=order_no,
    data=order_data
)
```

#### `setup_env.sh`
- 移除了`pip3 install pymysql`
- 保留了requests和python-dotenv的安装

### 3. 数据字段映射

Directus中的`forders`集合字段与原MySQL表结构保持一致：
- `orderNo` - 订单号（主键）
- `outStatus` - 出货状态
- `businessStatus` - 业务状态
- `createTime` - 创建时间
- `updateTime` - 更新时间
- `deliveryTime` - 发货时间
- `channel_config` - 渠道配置（外键）
- `target` - 目标手机号
- `send_goods_result` - 发货结果JSON
- `Phone_Card` - 号卡商品（外键）
- `send_goods_result_Alias` - 发货结果描述
- `logisticsStatus` - 物流状态

### 4. 环境配置

使用的环境变量：
- `DIRECTUS_API_URL` - Directus API地址
- `DIRECTUS_TOKEN` - Directus访问令牌
- 移除了MySQL相关配置的使用

## 优势

1. **权限问题解决** - 不再需要直接访问远程MySQL服务器
2. **API统一** - 通过Directus API统一数据访问接口
3. **更好的错误处理** - Directus API提供更详细的错误信息
4. **维护性提升** - 减少了数据库连接管理的复杂性
5. **扩展性** - 可以轻松添加其他Directus集合的操作

## 测试结果

所有测试均通过：
- ✅ Directus连接测试
- ✅ upsert功能测试
- ✅ 采集器集成测试

## 使用方法

1. 确保环境变量配置正确
2. 运行`bash setup_env.sh`安装依赖
3. 使用原有的运行方式启动采集器

## 注意事项

1. 确保Directus中的`forders`和相关集合权限配置正确
2. 外键约束需要使用有效的关联数据
3. 时间格式需要符合ISO 8601标准
4. JSON字段需要使用`json.dumps()`进行序列化

## 兼容性

- 保持了原有的接口和方法签名
- 数据结构完全兼容
- 日志输出格式保持一致
- 返回值类型保持不变
