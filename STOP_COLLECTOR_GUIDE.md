# 数据采集器停止指南

## 概述
为了方便在群晖上管理正在运行的数据采集器，我们提供了多种停止方法。

## 方法1：使用停止脚本（推荐）

### 安装依赖
```bash
# 如果还没有安装psutil
pip install psutil
```

### 使用方法
```bash
# 查看正在运行的采集器状态
python stop_collector.py --status

# 停止所有正在运行的采集器
python stop_collector.py
```

### 特点
- ✅ 优雅停止，不会丢失数据
- ✅ 自动查找所有相关进程
- ✅ 支持强制终止（如果优雅停止失败）
- ✅ 详细的状态反馈

## 方法2：通过群晖控制面板

### 步骤
1. **打开群晖控制面板**
2. **进入"任务计划"**
3. **找到正在运行的数据采集任务**
   - 状态显示为"运行中"
   - 可以看到开始时间和运行时长
4. **停止任务**
   - 选中任务
   - 点击"停止"按钮
   - 确认操作

### 特点
- ✅ 图形界面，操作简单
- ✅ 可以看到任务运行状态
- ⚠️ 可能需要等待当前批次处理完成

## 方法3：SSH命令行

### 查找进程
```bash
# 查找Python进程
ps aux | grep python

# 查找采集器进程
ps aux | grep fengzs_collector

# 更详细的查找
ps aux | grep -E "(python|fengzs)"
```

### 停止进程
```bash
# 优雅停止（推荐）
kill -TERM <进程ID>

# 强制停止（如果优雅停止无效）
kill -KILL <进程ID>

# 按名称停止
pkill -f "fengzs_collector"

# 强制按名称停止
pkill -9 -f "fengzs_collector"
```

### 特点
- ✅ 直接控制，响应快速
- ⚠️ 需要SSH访问权限
- ⚠️ 需要手动查找进程ID

## 方法4：创建停止信号文件

### 使用方法
```bash
# 在项目目录下创建停止信号文件
touch .stop_collector

# 采集器会在下一次检查时自动停止
# 停止后文件会被自动删除
```

### 特点
- ✅ 最温和的停止方式
- ✅ 不会中断正在处理的批次
- ⚠️ 可能需要等待较长时间

## 优雅停止机制

### 采集器内置的停止检查点
1. **时间区间切换时**：处理完当前时间区间后检查
2. **渠道切换时**：处理完当前渠道后检查
3. **批次处理间**：每个批次之间检查
4. **信号处理**：收到SIGTERM或SIGINT信号时立即响应

### 停止过程
1. **收到停止信号**
2. **完成当前批次处理**
3. **保存已处理的数据**
4. **清理资源**
5. **记录停止日志**
6. **优雅退出**

## 群晖定时任务最佳实践

### 设置任务超时
在群晖任务计划中设置合理的超时时间：
- **短期采集**：30分钟
- **长期采集**：2小时
- **大量数据**：4小时

### 监控任务状态
1. **启用邮件通知**：任务完成或失败时发送邮件
2. **定期检查日志**：查看`logs/`目录下的日志文件
3. **设置监控脚本**：定期检查任务是否正常运行

### 避免重复运行
1. **设置合理的运行间隔**：确保上一次任务完成后再启动新任务
2. **使用锁文件**：防止同时运行多个实例
3. **监控进程状态**：启动前检查是否已有进程在运行

## 故障排除

### 无法停止的情况
1. **检查进程状态**：
   ```bash
   ps aux | grep fengzs_collector
   ```

2. **查看进程详情**：
   ```bash
   ps -ef | grep <进程ID>
   ```

3. **强制终止**：
   ```bash
   kill -9 <进程ID>
   ```

### 数据完整性检查
停止后建议检查：
1. **日志文件**：确认最后处理的数据
2. **数据库状态**：验证数据完整性
3. **错误记录**：查看是否有处理失败的订单

### 重启采集器
停止后重新启动：
1. **等待完全停止**：确保所有进程都已结束
2. **检查环境**：确认配置文件和依赖正常
3. **启动新任务**：通过群晖控制面板或命令行启动

## 日志分析

### 停止相关的日志信息
```
收到停止信号，中断数据采集
收到停止信号，中断渠道处理
检测到停止信号文件，准备停止...
数据采集器已优雅停止
```

### 查看停止日志
```bash
# 查看最新的采集日志
tail -f logs/collector_$(ls logs/collector_*.log | tail -1 | cut -d'/' -f2)

# 搜索停止相关日志
grep -i "停止\|stop\|signal" logs/collector_*.log
```

## 性能影响

### 优雅停止的优势
- ✅ 不会丢失正在处理的数据
- ✅ 保持数据一致性
- ✅ 避免数据库连接异常
- ✅ 记录完整的处理状态

### 强制停止的风险
- ⚠️ 可能丢失当前批次数据
- ⚠️ 可能导致数据不一致
- ⚠️ 需要手动检查数据完整性
