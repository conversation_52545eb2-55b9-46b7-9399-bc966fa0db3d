# 群晖NAS部署指南

## 部署步骤

### 1. 文件上传
将整个项目文件夹上传到群晖NAS，建议路径：
```
/volume1/your_project_name/forder1/
```

### 2. 环境配置
确保`.env`文件包含正确的配置：
```bash
# Directus配置
DIRECTUS_TOKEN=OppexW5M7FRYT3VQHT3EQx8x3Ly6k2ZM
DIRECTUS_API_URL=https://api.mendeleev.cn

# 其他配置...
BEGIN_DATE=2025-06-01 00:00:00
END_DATE=2025-07-31 23:59:59
# ... 其他配置保持不变
```

### 3. 初始化环境
通过SSH连接到群晖，执行：
```bash
cd /volume1/your_project_name/forder1/
bash setup_env.sh
```

### 4. 测试运行
手动测试一次：
```bash
bash run_collector.sh
```

### 5. 设置定时任务

#### 方法一：使用群晖控制面板（推荐）
1. 打开群晖控制面板
2. 进入"任务计划"
3. 新增 → 计划的任务 → 用户定义的脚本
4. 配置如下：
   - **任务名称**: 数据采集器
   - **用户**: root（或有权限的用户）
   - **计划**: 根据需要设置（如每小时执行一次）
   - **任务设置** → **运行命令**:
     ```bash
     bash /volume1/your_project_name/forder1/synology_cron.sh
     ```

#### 方法二：使用crontab
通过SSH连接群晖，编辑crontab：
```bash
sudo crontab -e
```

添加定时任务（例如每小时执行一次）：
```bash
0 * * * * bash /volume1/your_project_name/forder1/synology_cron.sh
```

### 6. 修改synology_cron.sh
编辑`synology_cron.sh`文件，修改项目路径：
```bash
# 设置项目路径（请根据实际情况修改）
PROJECT_PATH="/volume1/your_project_name/forder1"
```

## 脚本说明

### setup_env.sh
- 环境初始化脚本
- 创建Python虚拟环境
- 安装依赖包
- 只需运行一次

### run_collector.sh
- 手动运行脚本
- 包含完整的错误检查
- 生成详细的运行日志
- 适合调试和手动执行

### synology_cron.sh
- 群晖定时任务专用脚本
- 简化的错误处理
- 自动日志清理
- 适合无人值守运行

## 日志管理

### 日志文件位置
- 运行日志: `logs/run_YYYYMMDD_HHMMSS.log`
- 定时任务日志: `logs/cron_YYYYMMDD_HHMMSS.log`
- 采集器日志: `logs/collector_YYYYMMDD_HHMMSS.log`
- 请求日志: `logs/request_YYYYMMDD_HHMMSS.log`

### 自动清理
- `run_collector.sh`: 保留7天日志
- `synology_cron.sh`: 保留30天日志

## 故障排除

### 1. 权限问题
如果遇到权限问题，确保脚本有执行权限：
```bash
chmod +x setup_env.sh
chmod +x run_collector.sh
chmod +x synology_cron.sh
```

### 2. Python环境问题
检查Python3是否安装：
```bash
python3 --version
```

如果没有Python3，需要安装Python套件包。

### 3. 网络连接问题
测试Directus API连接：
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" https://api.mendeleev.cn/items/forders?limit=1
```

### 4. 依赖问题
重新安装依赖：
```bash
rm -rf venv
bash setup_env.sh
```

### 5. 查看日志
检查最新的日志文件：
```bash
tail -f logs/collector_$(ls logs/collector_*.log | tail -1 | cut -d'/' -f2)
```

## 监控建议

1. **设置邮件通知**: 在群晖任务计划中启用邮件通知
2. **定期检查日志**: 建议每周检查一次日志文件
3. **监控磁盘空间**: 确保日志目录有足够空间
4. **备份配置**: 定期备份`.env`和其他配置文件

## 性能优化

1. **调整运行频率**: 根据数据更新频率调整定时任务间隔
2. **日志级别**: 生产环境可以关闭详细日志（verbose_logging=False）
3. **网络优化**: 如果网络不稳定，可以增加重试次数和间隔时间

## 安全注意事项

1. **保护.env文件**: 确保.env文件权限设置为600
2. **定期更新Token**: 定期更换Directus访问令牌
3. **网络安全**: 确保群晖防火墙配置正确
4. **备份策略**: 建立完整的数据备份策略
