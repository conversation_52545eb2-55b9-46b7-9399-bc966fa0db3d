#!/bin/bash

# 群晖定时任务专用脚本
# 在群晖任务计划中使用: bash /path/to/your/project/synology_cron.sh

# 设置项目路径（请根据实际情况修改）
PROJECT_PATH="/volume1/your_project_path/forder1"

# 切换到项目目录
cd "$PROJECT_PATH" || {
    echo "错误: 无法切换到项目目录 $PROJECT_PATH"
    exit 1
}

# 设置环境变量
export PATH="/usr/local/bin:/usr/bin:/bin:$PATH"

# 创建日志目录
mkdir -p logs

# 设置日志文件
CRON_LOG="logs/cron_$(date +%Y%m%d_%H%M%S).log"

# 记录开始时间
echo "$(date): 群晖定时任务开始执行" >> "$CRON_LOG"
echo "项目路径: $PROJECT_PATH" >> "$CRON_LOG"
echo "当前用户: $(whoami)" >> "$CRON_LOG"
echo "Python路径: $(which python3)" >> "$CRON_LOG"

# 检查必要文件
if [ ! -f ".env" ]; then
    echo "$(date): 错误 - 未找到.env配置文件" >> "$CRON_LOG"
    exit 1
fi

if [ ! -f "fengzs_collector.py" ]; then
    echo "$(date): 错误 - 未找到采集器主程序" >> "$CRON_LOG"
    exit 1
fi

if [ ! -d "venv" ]; then
    echo "$(date): 错误 - 未找到Python虚拟环境，请先运行setup_env.sh" >> "$CRON_LOG"
    exit 1
fi

# 激活虚拟环境并运行采集器
echo "$(date): 激活虚拟环境并启动采集器" >> "$CRON_LOG"

# 使用绝对路径激活虚拟环境
source "$PROJECT_PATH/venv/bin/activate"

if [ $? -ne 0 ]; then
    echo "$(date): 错误 - 激活虚拟环境失败" >> "$CRON_LOG"
    exit 1
fi

# 运行采集器
python fengzs_collector.py >> "$CRON_LOG" 2>&1
EXIT_CODE=$?

# 记录结束状态
if [ $EXIT_CODE -eq 0 ]; then
    echo "$(date): 采集器运行成功完成" >> "$CRON_LOG"
else
    echo "$(date): 采集器运行失败，退出码: $EXIT_CODE" >> "$CRON_LOG"
fi

# 退出虚拟环境
deactivate

# 清理旧日志（保留最近30天）
find "$PROJECT_PATH/logs" -name "cron_*.log" -type f -mtime +30 -delete 2>/dev/null
find "$PROJECT_PATH/logs" -name "collector_*.log" -type f -mtime +30 -delete 2>/dev/null
find "$PROJECT_PATH/logs" -name "request_*.log" -type f -mtime +30 -delete 2>/dev/null

echo "$(date): 定时任务执行完成" >> "$CRON_LOG"
exit $EXIT_CODE
