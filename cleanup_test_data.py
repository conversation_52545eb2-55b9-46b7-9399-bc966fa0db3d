#!/usr/bin/env python3
"""
清理测试数据脚本
"""

import os
import sys
from dotenv import load_dotenv
from directus_client import DirectusClient
import logging

# 加载环境变量
load_dotenv()

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def cleanup_test_data():
    """清理所有测试数据"""
    logger = setup_logging()
    
    # 获取配置
    directus_api_url = os.getenv('DIRECTUS_API_URL')
    directus_token = os.getenv('DIRECTUS_TOKEN')
    
    if not directus_api_url or not directus_token:
        logger.error("缺少Directus配置信息")
        return False
    
    logger.info("开始清理测试数据...")
    
    try:
        # 创建Directus客户端
        client = DirectusClient(
            api_url=directus_api_url,
            token=directus_token,
            logger=logger
        )
        
        # 查找所有测试数据
        test_patterns = [
            'TEST_ORDER_%',
            'PERF_TEST_%',
            'BATCH_TEST_%',
            'OPT_TEST_%',
            'TEST_UPSERT_%',
            'TEST_COLLECTOR_%'
        ]
        
        total_deleted = 0
        
        for pattern in test_patterns:
            logger.info(f"查找模式: {pattern}")
            
            try:
                # 查询匹配的测试数据
                test_orders = client.query_items(
                    collection='forders',
                    filter_params={
                        'filter[orderNo][_starts_with]': pattern.replace('%', '')
                    },
                    fields=['orderNo'],
                    limit=1000
                )
                
                if test_orders:
                    logger.info(f"找到 {len(test_orders)} 条测试数据")
                    
                    # 删除测试数据
                    deleted_count = 0
                    for order in test_orders:
                        try:
                            if client.delete_item('forders', order['orderNo']):
                                deleted_count += 1
                            else:
                                logger.warning(f"删除失败: {order['orderNo']}")
                        except Exception as e:
                            logger.error(f"删除订单 {order['orderNo']} 失败: {str(e)}")
                    
                    logger.info(f"成功删除 {deleted_count} 条数据")
                    total_deleted += deleted_count
                else:
                    logger.info("未找到匹配的测试数据")
                    
            except Exception as e:
                logger.error(f"处理模式 {pattern} 时出错: {str(e)}")
        
        logger.info(f"清理完成，总共删除 {total_deleted} 条测试数据")
        return True
        
    except Exception as e:
        logger.error(f"清理测试数据失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = cleanup_test_data()
    sys.exit(0 if success else 1)
