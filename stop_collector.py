#!/usr/bin/env python3
"""
数据采集器停止脚本
用于优雅地停止正在运行的采集器
"""

import os
import sys
import signal
import time
import psutil
from pathlib import Path

def find_collector_processes():
    """查找正在运行的采集器进程"""
    collector_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            # 检查是否是Python进程且运行的是采集器
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if 'fengzs_collector' in cmdline:
                    collector_processes.append({
                        'pid': proc.info['pid'],
                        'cmdline': cmdline,
                        'process': proc
                    })
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    return collector_processes

def create_stop_signal_file():
    """创建停止信号文件"""
    stop_file = Path(__file__).parent / '.stop_collector'
    stop_file.touch()
    print(f"已创建停止信号文件: {stop_file}")
    return stop_file

def remove_stop_signal_file():
    """删除停止信号文件"""
    stop_file = Path(__file__).parent / '.stop_collector'
    if stop_file.exists():
        stop_file.unlink()
        print(f"已删除停止信号文件: {stop_file}")

def stop_collector_gracefully():
    """优雅地停止采集器"""
    print("正在查找运行中的数据采集器进程...")
    
    processes = find_collector_processes()
    
    if not processes:
        print("未找到正在运行的数据采集器进程")
        return True
    
    print(f"找到 {len(processes)} 个采集器进程:")
    for i, proc_info in enumerate(processes, 1):
        print(f"  {i}. PID: {proc_info['pid']}")
        print(f"     命令: {proc_info['cmdline']}")
    
    # 创建停止信号文件
    stop_file = create_stop_signal_file()
    
    print("\n开始优雅停止进程...")
    
    # 发送SIGTERM信号
    for proc_info in processes:
        try:
            proc = proc_info['process']
            print(f"向进程 {proc_info['pid']} 发送停止信号...")
            proc.terminate()
        except psutil.NoSuchProcess:
            print(f"进程 {proc_info['pid']} 已经结束")
        except psutil.AccessDenied:
            print(f"没有权限终止进程 {proc_info['pid']}")
    
    # 等待进程结束
    print("等待进程优雅退出...")
    max_wait_time = 30  # 最多等待30秒
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        remaining_processes = find_collector_processes()
        if not remaining_processes:
            print("✅ 所有采集器进程已优雅退出")
            remove_stop_signal_file()
            return True
        
        print(f"还有 {len(remaining_processes)} 个进程在运行，继续等待...")
        time.sleep(2)
    
    # 如果还有进程在运行，强制终止
    remaining_processes = find_collector_processes()
    if remaining_processes:
        print("⚠️ 优雅退出超时，开始强制终止...")
        for proc_info in remaining_processes:
            try:
                proc = proc_info['process']
                print(f"强制终止进程 {proc_info['pid']}...")
                proc.kill()
            except psutil.NoSuchProcess:
                print(f"进程 {proc_info['pid']} 已经结束")
            except psutil.AccessDenied:
                print(f"没有权限强制终止进程 {proc_info['pid']}")
        
        # 再次检查
        time.sleep(2)
        final_processes = find_collector_processes()
        if not final_processes:
            print("✅ 所有进程已被强制终止")
            remove_stop_signal_file()
            return True
        else:
            print("❌ 仍有进程无法终止，请手动处理")
            for proc_info in final_processes:
                print(f"  PID: {proc_info['pid']} - {proc_info['cmdline']}")
            return False
    
    remove_stop_signal_file()
    return True

def main():
    print("数据采集器停止工具")
    print("=" * 40)
    
    if len(sys.argv) > 1 and sys.argv[1] == '--status':
        # 只查看状态，不停止
        processes = find_collector_processes()
        if processes:
            print(f"找到 {len(processes)} 个正在运行的采集器进程:")
            for i, proc_info in enumerate(processes, 1):
                print(f"  {i}. PID: {proc_info['pid']}")
                print(f"     命令: {proc_info['cmdline']}")
        else:
            print("没有找到正在运行的采集器进程")
        return
    
    # 确认操作
    response = input("确定要停止所有正在运行的数据采集器吗？(y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("操作已取消")
        return
    
    success = stop_collector_gracefully()
    
    if success:
        print("\n🎉 数据采集器已成功停止")
    else:
        print("\n❌ 部分进程可能仍在运行，请检查系统进程")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"\n发生错误: {str(e)}")
        sys.exit(1)
