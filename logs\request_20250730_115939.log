2025-07-30 11:59:39,003 - <PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - 请求详细日志已启用，将记录到request.log文件
2025-07-30 11:59:39,004 - <PERSON><PERSON><PERSON>ollector - INFO - 使用agent_id: 22032518oQft
2025-07-30 11:59:39,004 - <PERSON><PERSON><PERSON>ollector - INFO - 成功加载hive cookie
2025-07-30 11:59:39,005 - <PERSON><PERSON><PERSON><PERSON>ector - INFO - 成功加载cookie
2025-07-30 11:59:39,005 - <PERSON><PERSON><PERSON>ollector - INFO - 原始采集时间区间设置为: 2025-06-01 00:00:00 至 2025-07-31 23:59:59
2025-07-30 11:59:39,005 - <PERSON><PERSON><PERSON>ollector - INFO - 渠道请求间隔设置为: 1.0秒
2025-07-30 11:59:39,005 - <PERSON>ZSCollector - INFO - 分页请求间隔设置为: 0.5秒
2025-07-30 11:59:39,005 - <PERSON>Z<PERSON>ollector - INFO - FengZSCollector 初始化完成
2025-07-30 11:59:39,008 - <PERSON><PERSON><PERSON>ollector - INFO - 开始获取渠道配置映射
2025-07-30 11:59:39,010 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.mendeleev.cn:443
2025-07-30 11:59:39,445 - urllib3.connectionpool - DEBUG - https://api.mendeleev.cn:443 "GET /items/channel_config?fields=id&fields=channel_id&fields=channel_name&fields=description HTTP/1.1" 200 None
2025-07-30 11:59:39,446 - FengZSCollector - INFO - 成功获取到 34 个渠道配置映射
2025-07-30 11:59:39,449 - FengZSCollector - INFO - 处理订单 TEST_COLLECTOR_ORDER_001
2025-07-30 11:59:39,449 - FengZSCollector - DEBUG - 订单 TEST_COLLECTOR_ORDER_001 详情: 业务状态=交易成功, 出货状态=已激活, 物流状态=发货成功
2025-07-30 11:59:39,455 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.mendeleev.cn:443
2025-07-30 11:59:39,819 - urllib3.connectionpool - DEBUG - https://api.mendeleev.cn:443 "GET /items/forders/TEST_COLLECTOR_ORDER_001 HTTP/1.1" 403 102
2025-07-30 11:59:39,820 - FengZSCollector - DEBUG - GET https://api.mendeleev.cn/items/forders/TEST_COLLECTOR_ORDER_001 - Status: 403
2025-07-30 11:59:39,820 - FengZSCollector - ERROR - Directus API请求失败: GET https://api.mendeleev.cn/items/forders/TEST_COLLECTOR_ORDER_001 - 403 - {'errors': [{'message': "You don't have permission to access this.", 'extensions': {'code': 'FORBIDDEN'}}]}
2025-07-30 11:59:39,821 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.mendeleev.cn:443
2025-07-30 11:59:40,216 - urllib3.connectionpool - DEBUG - https://api.mendeleev.cn:443 "POST /items/forders HTTP/1.1" 200 454
2025-07-30 11:59:40,216 - FengZSCollector - DEBUG - POST https://api.mendeleev.cn/items/forders - Status: 200
2025-07-30 11:59:40,217 - FengZSCollector - INFO - 订单 TEST_COLLECTOR_ORDER_001 新增成功
2025-07-30 11:59:40,218 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.mendeleev.cn:443
2025-07-30 11:59:40,572 - urllib3.connectionpool - DEBUG - https://api.mendeleev.cn:443 "DELETE /items/forders/TEST_COLLECTOR_ORDER_001 HTTP/1.1" 204 0
2025-07-30 11:59:40,572 - FengZSCollector - DEBUG - DELETE https://api.mendeleev.cn/items/forders/TEST_COLLECTOR_ORDER_001 - Status: 204
