#!/usr/bin/env python3
"""
测试Directus连接和基本功能的脚本
"""

import os
import sys
import time
from dotenv import load_dotenv
from directus_client import DirectusClient
import logging

# 加载环境变量
load_dotenv()

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_directus_connection():
    """测试Directus连接"""
    logger = setup_logging()
    
    # 获取配置
    directus_api_url = os.getenv('DIRECTUS_API_URL')
    directus_token = os.getenv('DIRECTUS_TOKEN')
    
    if not directus_api_url or not directus_token:
        logger.error("缺少Directus配置信息")
        return False
    
    logger.info(f"测试连接到Directus: {directus_api_url}")
    
    try:
        # 创建Directus客户端
        client = DirectusClient(
            api_url=directus_api_url,
            token=directus_token,
            logger=logger
        )
        
        # 测试1: 获取forders集合的一条记录
        logger.info("测试1: 获取forders集合数据...")
        orders = client.query_items(
            collection='forders',
            limit=1,
            fields=['orderNo', 'businessStatus', 'createTime']
        )
        
        if orders:
            logger.info(f"✅ 成功获取到 {len(orders)} 条订单记录")
            logger.info(f"示例订单: {orders[0]}")
        else:
            logger.warning("⚠️ forders集合中没有数据")
        
        # 测试2: 获取channel_config集合数据
        logger.info("测试2: 获取channel_config集合数据...")
        channels = client.query_items(
            collection='channel_config',
            limit=3,
            fields=['id', 'channel_id', 'channel_name']
        )
        
        if channels:
            logger.info(f"✅ 成功获取到 {len(channels)} 条渠道配置")
            for channel in channels:
                logger.info(f"渠道: {channel}")
        else:
            logger.warning("⚠️ channel_config集合中没有数据")
        
        # 测试3: 测试获取单个订单
        logger.info("测试3: 测试获取单个订单...")
        if orders:
            first_order_no = orders[0]['orderNo']
            single_order = client.get_item('forders', first_order_no)
            if single_order:
                logger.info(f"✅ 成功获取单个订单: {single_order['orderNo']}")
            else:
                logger.warning("⚠️ 获取单个订单失败")

        logger.info("ℹ️ 跳过写操作测试（需要更高权限）")
        
        logger.info("🎉 所有测试通过！Directus连接正常")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_directus_connection()
    sys.exit(0 if success else 1)
