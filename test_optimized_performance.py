#!/usr/bin/env python3
"""
优化后的性能测试脚本
"""

import os
import sys
import time
import json
from dotenv import load_dotenv
from directus_client import DirectusClient
import logging

# 加载环境变量
load_dotenv()

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_optimized_batch_upsert():
    """测试优化后的批量upsert性能"""
    logger = setup_logging()
    
    # 获取配置
    directus_api_url = os.getenv('DIRECTUS_API_URL')
    directus_token = os.getenv('DIRECTUS_TOKEN')
    
    if not directus_api_url or not directus_token:
        logger.error("缺少Directus配置信息")
        return False
    
    logger.info("开始优化后的批量upsert性能测试...")
    
    try:
        # 创建Directus客户端
        client = DirectusClient(
            api_url=directus_api_url,
            token=directus_token,
            logger=logger
        )
        
        # 生成测试数据
        test_count = 100
        base_time = int(time.time())
        test_data = []
        
        for i in range(test_count):
            # 使用更精确的时间戳避免重复
            unique_time = int(time.time() * 1000) + i  # 毫秒级时间戳
            order_data = {
                'orderNo': f'OPT_TEST_{unique_time}_{i:04d}',
                'outStatus': '已激活',
                'businessStatus': '交易成功',
                'createTime': '2025-01-01T00:00:00',
                'updateTime': '2025-01-01T00:00:00',
                'target': f'138{i:08d}',
                'send_goods_result': json.dumps({"test": f"opt_data_{i}"}, ensure_ascii=False),
                'send_goods_result_Alias': f'优化测试描述_{i}',
                'logisticsStatus': '发货成功',
                'channel_config': 5,
                'Phone_Card': 'BJYD_58Y_SFQSK'
            }
            test_data.append(order_data)
        
        logger.info(f"生成测试数据: {len(test_data)} 条")
        
        # 测试批量upsert性能
        start_time = time.time()
        
        result = client.batch_upsert_items(
            collection='forders',
            items=test_data,
            primary_key_field='orderNo'
        )
        
        total_time = time.time() - start_time
        
        # 输出结果
        logger.info(f"批量upsert完成:")
        logger.info(f"  总耗时: {total_time:.2f}秒")
        logger.info(f"  创建: {result['created']} 条")
        logger.info(f"  更新: {result['updated']} 条")
        logger.info(f"  失败: {result['failed']} 条")
        logger.info(f"  平均耗时: {total_time/test_count:.4f}秒/条")
        logger.info(f"  处理速度: {test_count/total_time:.2f}条/秒")
        logger.info(f"  预计每小时处理: {int(3600 * test_count / total_time)} 条")
        
        # 测试更新性能（重复插入相同数据）
        logger.info("\n测试更新性能（重复插入相同数据）...")
        update_start_time = time.time()
        
        update_result = client.batch_upsert_items(
            collection='forders',
            items=test_data,
            primary_key_field='orderNo'
        )
        
        update_time = time.time() - update_start_time
        
        logger.info(f"批量更新完成:")
        logger.info(f"  总耗时: {update_time:.2f}秒")
        logger.info(f"  创建: {update_result['created']} 条")
        logger.info(f"  更新: {update_result['updated']} 条")
        logger.info(f"  失败: {update_result['failed']} 条")
        logger.info(f"  平均耗时: {update_time/test_count:.4f}秒/条")
        logger.info(f"  处理速度: {test_count/update_time:.2f}条/秒")
        
        # 清理测试数据
        logger.info("\n清理测试数据...")
        cleanup_start = time.time()
        for order_data in test_data:
            try:
                client.delete_item('forders', order_data['orderNo'])
            except Exception as e:
                logger.warning(f"删除失败: {order_data['orderNo']} - {str(e)}")
        
        cleanup_time = time.time() - cleanup_start
        logger.info(f"清理完成，耗时: {cleanup_time:.2f}秒")
        
        # 性能总结
        logger.info(f"\n{'='*50}")
        logger.info("性能测试总结:")
        logger.info(f"数据量: {test_count} 条")
        logger.info(f"首次插入: {total_time:.2f}秒 ({test_count/total_time:.2f}条/秒)")
        logger.info(f"重复更新: {update_time:.2f}秒 ({test_count/update_time:.2f}条/秒)")
        logger.info(f"预计15分钟可处理: {int(900 * test_count / total_time)} 条订单")
        logger.info(f"预计1小时可处理: {int(3600 * test_count / total_time)} 条订单")
        logger.info(f"{'='*50}")
        
        return True
        
    except Exception as e:
        logger.error(f"性能测试失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = test_optimized_batch_upsert()
    sys.exit(0 if success else 1)
