#!/usr/bin/env python3
"""
性能测试脚本 - 测试不同延时配置下的采集性能
"""

import os
import sys
import time
import json
from dotenv import load_dotenv
from directus_client import DirectusClient
import logging

# 加载环境变量
load_dotenv()

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_directus_performance():
    """测试Directus API性能"""
    logger = setup_logging()
    
    # 获取配置
    directus_api_url = os.getenv('DIRECTUS_API_URL')
    directus_token = os.getenv('DIRECTUS_TOKEN')
    
    if not directus_api_url or not directus_token:
        logger.error("缺少Directus配置信息")
        return False
    
    logger.info("开始Directus API性能测试...")
    
    try:
        # 创建Directus客户端
        client = DirectusClient(
            api_url=directus_api_url,
            token=directus_token,
            logger=logger
        )
        
        # 测试1: 批量查询性能
        logger.info("测试1: 批量查询性能")
        start_time = time.time()
        
        # 查询不同数量的记录
        for limit in [10, 50, 100]:
            query_start = time.time()
            orders = client.query_items(
                collection='forders',
                limit=limit,
                fields=['orderNo', 'businessStatus', 'createTime']
            )
            query_time = time.time() - query_start
            logger.info(f"查询 {limit} 条记录耗时: {query_time:.2f}秒")
        
        total_query_time = time.time() - start_time
        logger.info(f"总查询时间: {total_query_time:.2f}秒")
        
        # 测试2: 单个upsert操作性能
        logger.info("测试2: 单个upsert操作性能")
        test_orders = []
        
        # 准备测试数据
        for i in range(10):
            test_order_no = f"PERF_TEST_{int(time.time())}_{i}"
            test_orders.append({
                'orderNo': test_order_no,
                'outStatus': '测试状态',
                'businessStatus': '测试业务状态',
                'createTime': '2025-01-01T00:00:00',
                'updateTime': '2025-01-01T00:00:00',
                'target': '***********',
                'send_goods_result': json.dumps({"test": f"data_{i}"}, ensure_ascii=False),
                'send_goods_result_Alias': f'测试描述_{i}',
                'logisticsStatus': '未发货',
                'channel_config': 5,
                'Phone_Card': 'BJYD_58Y_SFQSK'
            })
        
        # 测试单个upsert性能
        upsert_times = []
        for order_data in test_orders:
            upsert_start = time.time()
            result, is_update = client.upsert_item(
                collection='forders',
                primary_key=order_data['orderNo'],
                data=order_data
            )
            upsert_time = time.time() - upsert_start
            upsert_times.append(upsert_time)
            
            if is_update:
                logger.debug(f"更新订单 {order_data['orderNo']} 耗时: {upsert_time:.3f}秒")
            else:
                logger.debug(f"创建订单 {order_data['orderNo']} 耗时: {upsert_time:.3f}秒")
        
        # 统计upsert性能
        avg_upsert_time = sum(upsert_times) / len(upsert_times)
        max_upsert_time = max(upsert_times)
        min_upsert_time = min(upsert_times)
        
        logger.info(f"平均upsert时间: {avg_upsert_time:.3f}秒")
        logger.info(f"最大upsert时间: {max_upsert_time:.3f}秒")
        logger.info(f"最小upsert时间: {min_upsert_time:.3f}秒")
        logger.info(f"预计每小时可处理订单: {int(3600 / avg_upsert_time)} 条")
        
        # 清理测试数据
        logger.info("清理测试数据...")
        cleanup_start = time.time()
        for order_data in test_orders:
            client.delete_item('forders', order_data['orderNo'])
        cleanup_time = time.time() - cleanup_start
        logger.info(f"清理 {len(test_orders)} 条测试数据耗时: {cleanup_time:.2f}秒")
        
        # 性能总结
        logger.info("=== 性能测试总结 ===")
        logger.info(f"单次upsert平均耗时: {avg_upsert_time:.3f}秒")
        logger.info(f"理论最大处理速度: {int(1 / avg_upsert_time)} 条/秒")
        logger.info(f"建议批量大小: {min(100, max(20, int(1 / avg_upsert_time * 10)))} 条")
        
        return True
        
    except Exception as e:
        logger.error(f"性能测试失败: {str(e)}")
        return False

def test_api_delay_impact():
    """测试不同延时配置对性能的影响"""
    logger = setup_logging()
    
    logger.info("测试不同延时配置的性能影响...")
    
    # 模拟不同的延时配置
    delay_configs = [
        {"name": "无延时", "channel": 0, "page": 0},
        {"name": "快速模式", "channel": 0.5, "page": 0.3},
        {"name": "标准模式", "channel": 1.0, "page": 0.5},
        {"name": "保守模式", "channel": 2.0, "page": 1.0}
    ]
    
    # 模拟处理5个渠道，每个渠道3页数据
    channels = 5
    pages_per_channel = 3
    
    for config in delay_configs:
        start_time = time.time()
        
        # 模拟渠道处理
        for channel in range(channels):
            # 模拟页面处理
            for page in range(pages_per_channel):
                if page > 0:  # 第一页不延时
                    time.sleep(config["page"])
                # 模拟API处理时间
                time.sleep(0.1)  # 假设每个API调用需要0.1秒
            
            # 渠道间延时
            if channel < channels - 1:
                time.sleep(config["channel"])
        
        total_time = time.time() - start_time
        logger.info(f"{config['name']}: 总耗时 {total_time:.2f}秒")
    
    return True

if __name__ == "__main__":
    logger = setup_logging()
    
    logger.info("开始性能测试...")
    
    # 测试Directus性能
    if test_directus_performance():
        logger.info("✅ Directus性能测试通过")
    else:
        logger.error("❌ Directus性能测试失败")
    
    # 测试延时配置影响
    if test_api_delay_impact():
        logger.info("✅ 延时配置测试完成")
    else:
        logger.error("❌ 延时配置测试失败")
    
    logger.info("性能测试完成！")
