#!/bin/bash

# 数据采集器运行脚本 - 适用于群晖定时任务
# 使用方法: bash run_collector.sh [参数]

# 设置工作目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

# 设置日志文件
LOG_FILE="logs/run_$(date +%Y%m%d_%H%M%S).log"
mkdir -p logs

# 记录开始时间
START_TIME=$(date)
echo "[$START_TIME] 开始运行数据采集器..." | tee -a "$LOG_FILE"
echo "工作目录: $SCRIPT_DIR" | tee -a "$LOG_FILE"

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo "错误: 虚拟环境不存在，请先运行 bash setup_env.sh" | tee -a "$LOG_FILE"
    exit 1
fi

# 检查环境配置文件
if [ ! -f ".env" ]; then
    echo "错误: 未找到.env配置文件" | tee -a "$LOG_FILE"
    exit 1
fi

# 激活 Python 虚拟环境
echo "激活Python虚拟环境..." | tee -a "$LOG_FILE"
source venv/bin/activate

if [ $? -ne 0 ]; then
    echo "错误: 激活虚拟环境失败" | tee -a "$LOG_FILE"
    exit 1
fi

# 检查Python依赖
echo "检查Python依赖..." | tee -a "$LOG_FILE"
python -c "import requests, dotenv; from directus_client import DirectusClient" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "错误: Python依赖检查失败，请重新运行 bash setup_env.sh" | tee -a "$LOG_FILE"
    deactivate
    exit 1
fi

# 运行收集器程序
echo "启动数据采集器..." | tee -a "$LOG_FILE"
python fengzs_collector.py "$@" 2>&1 | tee -a "$LOG_FILE"

# 获取程序退出状态
EXIT_CODE=$?
END_TIME=$(date)

# 记录结束信息
if [ $EXIT_CODE -eq 0 ]; then
    echo "[$END_TIME] 数据采集器运行成功完成" | tee -a "$LOG_FILE"
else
    echo "[$END_TIME] 数据采集器运行失败，退出码: $EXIT_CODE" | tee -a "$LOG_FILE"
fi

# 退出虚拟环境
deactivate

# 清理旧日志文件（保留最近7天）
find logs -name "run_*.log" -type f -mtime +7 -delete 2>/dev/null

echo "运行完成，详细日志请查看: $LOG_FILE"
exit $EXIT_CODE