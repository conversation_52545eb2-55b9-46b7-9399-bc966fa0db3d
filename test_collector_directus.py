#!/usr/bin/env python3
"""
测试修改后的采集器是否能正常使用Directus API
"""

import os
import sys
import json
from dotenv import load_dotenv
from fengzs_collector import FengZSCollector
import logging

# 加载环境变量
load_dotenv()

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_collector_directus_integration():
    """测试采集器与Directus的集成"""
    logger = setup_logging()
    
    logger.info("测试采集器与Directus的集成...")
    
    try:
        # 创建采集器实例（禁用自动登录，只测试Directus功能）
        collector = FengZSCollector(
            verbose_logging=True,
            auto_login=False,
            test_login_only=False
        )
        
        # 测试Directus客户端是否正确初始化
        if not hasattr(collector, 'directus_client'):
            logger.error("❌ 采集器未正确初始化Directus客户端")
            return False
        
        logger.info("✅ Directus客户端初始化成功")
        
        # 测试获取渠道配置映射
        logger.info("测试获取渠道配置映射...")
        channel_config_map = collector.get_channel_config_map()
        
        if channel_config_map:
            logger.info(f"✅ 成功获取到 {len(channel_config_map)} 个渠道配置")
            for channel_id, config in list(channel_config_map.items())[:3]:  # 只显示前3个
                logger.info(f"渠道: {channel_id} - {config.get('channel_name', 'N/A')}")
        else:
            logger.warning("⚠️ 未获取到渠道配置")
        
        # 测试upsert_order方法（使用模拟数据）
        logger.info("测试upsert_order方法...")
        test_order = {
            'orderNo': 'TEST_COLLECTOR_ORDER_001',
            'outStatus': 'ACTIVEOK',
            'businessStatus': 'FINISH',
            'createTime': '2025-01-01T00:00:00',
            'updateTime': '2025-01-01T00:00:00',
            'deliveryTime': '2025-01-01T01:00:00',
            'target': '***********',
            'sendGoodsResult': '{"status": "success", "message": "发货成功"}',
            'skuCode': 'BJYD_58Y_SFQSK',  # 使用有效的SKU
            'logisticsStatus': 'SHIPOK',
            'channel_config': 5  # 使用有效的渠道配置ID
        }
        
        # 执行upsert操作
        is_update = collector.upsert_order(test_order)
        
        if is_update is not None:
            if is_update:
                logger.info("✅ 订单更新成功")
            else:
                logger.info("✅ 订单创建成功")
        else:
            logger.error("❌ upsert_order返回了None")
            return False
        
        # 清理测试数据
        logger.info("清理测试数据...")
        if collector.directus_client.delete_item('forders', 'TEST_COLLECTOR_ORDER_001'):
            logger.info("✅ 测试数据清理成功")
        else:
            logger.warning("⚠️ 测试数据清理失败")
        
        logger.info("🎉 采集器Directus集成测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = test_collector_directus_integration()
    sys.exit(0 if success else 1)
