2025-07-30 11:14:48,230 - <PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - 使用agent_id: 22032518oQft
2025-07-30 11:14:48,230 - <PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - 成功加载hive cookie
2025-07-30 11:14:48,230 - <PERSON><PERSON><PERSON>ollector - INFO - 开始执行自动登录流程
2025-07-30 11:14:48,230 - <PERSON><PERSON><PERSON>ollector - INFO - 首次登录尝试
2025-07-30 11:14:48,231 - <PERSON><PERSON><PERSON><PERSON>ector - INFO - 访问hive主页
2025-07-30 11:14:48,231 - <PERSON><PERSON><PERSON>ollector - INFO - 添加hivecookie到会话
2025-07-30 11:14:48,231 - <PERSON><PERSON><PERSON><PERSON>ector - INFO - 成功添加 10 个cookie到会话
2025-07-30 11:14:48,704 - <PERSON><PERSON><PERSON>ollector - INFO - 请求getKey接口
2025-07-30 11:14:48,785 - <PERSON><PERSON><PERSON>ollector - INFO - getKey接口响应: 10001,8fcf9e0932fda4e83a22d3014c58883f835e539923d650c1cfcd7770ac61745ee79c4bd3fc4d0b3d4d543a300d5a0f
2025-07-30 11:14:48,785 - FengZSCollector - INFO - 访问login接口
2025-07-30 11:14:48,920 - FengZSCollector - INFO - 登录接口响应: {"code":1,"message":"操作成功"}
2025-07-30 11:14:48,921 - FengZSCollector - INFO - 登录成功
2025-07-30 11:14:48,921 - FengZSCollector - INFO - 访问agents页面，准备获取BSS cookie
2025-07-30 11:14:51,560 - FengZSCollector - INFO - 重定向历史: ['https://hive.phone580.com/agents/22032518oQft', 'https://bss.phone580.com/fzs-microbee-admin/sso-v2/49?token=XVbtAEdwxhx53OVcBG1AP5QRl%2BEIPcsRAXUsvfbl3f3GpUoufJ624RhsGyIa05U9g8XYAjuV7TtwdUdflHq9SCZ1E3pIBBEPGOiB175Ow3b0UfuiPipeQB9LJdbHTNS8dCa0pHUlvOKFGhc%2F6f1Nbc76FNqoa%2FWddLOGppWFlss%3D', 'https://bss.phone580.com/fzs-microbee-admin/sso-v2/49']
2025-07-30 11:14:51,561 - FengZSCollector - INFO - 最终URL: https://bss.phone580.com/bss-admin/
2025-07-30 11:14:51,561 - FengZSCollector - INFO - 成功跳转到BSS
2025-07-30 11:14:51,561 - FengZSCollector - INFO - 获取到的cookies: {'gdp_user_id': 'gioenc-bc4a9ce2%2C5gd5%2C57d8%2Cc667%2Cg3531d008d35', 'cna': '964986cc512742bbb56dda282d01d0f3', '8d2279a5e2f18b7c_gdp_sequence_ids': '{%22globalKey%22:55%2C%22VISIT%22:5%2C%22PAGE%22:11%2C%22CUSTOM%22:16%2C%22VIEW_CLICK%22:25%2C%22VIEW_CHANGE%22:2}', '8d2279a5e2f18b7c_gdp_session_id_sent': '9aec8988-1747-4a53-a66a-2d17a63f00a0', 'UM_distinctid': '1966bbbb1622417-061e1b537ebb73-26011c51-384000-1966bbbb1632868', 'userName': '%22%E8%B5%B5%E4%BB%95%E6%9D%B0%22', 'DEVICE_LOGIN_TOKEN': 'Y8Awd33jX2p5ZvdzVTY8mx2mTvHNq658+e4ET18JRfY=', 'SESSION_ID': 'EDA85CCB52177873AE950C12E15BB36F', 'acw_tc': 'ac11000117538391768085897e006c89e4fe097107b0b3da0cd090eca3fd97', 'JSESSIONID': '095BFE03FB9B436A1D1A33F29D6B7CD4', 'APP_ID': '49', 'SERVERID': 'c475eff2b2702eb905307c9f94bfbce0|1753845290|1753845290'}
2025-07-30 11:14:51,562 - FengZSCollector - INFO - 成功保存cookie到cookie.json文件
2025-07-30 11:14:51,562 - FengZSCollector - INFO - 登录成功！尝试次数: 1
2025-07-30 11:14:51,562 - FengZSCollector - INFO - 自动登录成功，已获取并保存cookie
2025-07-30 11:14:51,570 - FengZSCollector - INFO - 成功加载cookie
2025-07-30 11:14:51,570 - FengZSCollector - INFO - 原始采集时间区间设置为: 2025-06-01 00:00:00 至 2025-07-31 23:59:59
2025-07-30 11:14:51,575 - FengZSCollector - INFO - 渠道请求间隔设置为: 1.0秒
2025-07-30 11:14:51,576 - FengZSCollector - INFO - 分页请求间隔设置为: 0.5秒
2025-07-30 11:14:51,576 - FengZSCollector - INFO - FengZSCollector 初始化完成
2025-07-30 11:14:51,576 - FengZSCollector - INFO - 开始数据采集任务
2025-07-30 11:14:51,576 - FengZSCollector - INFO - 开始获取渠道配置映射
2025-07-30 11:15:12,625 - FengZSCollector - ERROR - 获取渠道配置映射失败: HTTPConnectionPool(host='*************', port=8055): Max retries exceeded with url: /items/channel_config?fields=id&fields=channel_id&fields=channel_name&fields=description (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x0000027CE3997E00>, 'Connection to ************* timed out. (connect timeout=None)'))
2025-07-30 11:15:12,626 - FengZSCollector - WARNING - 未获取到任何渠道ID，请检查Directus配置
