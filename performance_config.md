# 性能优化配置建议

## 当前优化内容

### 1. 延时优化
- **分页延时**: 只在第二页及以后添加延时，第一页不延时
- **渠道间延时**: 只在API请求之间延时，入库操作无延时
- **错误重试延时**: 从2-3秒减少到1-2秒
- **时间区间延时**: 从5秒减少到2秒

### 2. 批量处理优化
- **批量大小**: 从20条增加到50条，提高入库效率
- **批量进度**: 显示批次进度信息（如：第1/5批）

### 3. 入库性能优化
- **无延时入库**: 确保Directus API调用没有任何延时限制
- **批量处理**: 使用更大的批量大小减少API调用次数

## 环境变量配置建议

### 快速模式（推荐用于测试）
```bash
# 快速采集配置
CHANNEL_INTERVAL=0.5    # 渠道间延时0.5秒
PAGE_INTERVAL=0.3       # 分页延时0.3秒
```

### 标准模式（推荐用于生产）
```bash
# 标准采集配置
CHANNEL_INTERVAL=1.0    # 渠道间延时1秒
PAGE_INTERVAL=0.5       # 分页延时0.5秒
```

### 保守模式（网络不稳定时使用）
```bash
# 保守采集配置
CHANNEL_INTERVAL=2.0    # 渠道间延时2秒
PAGE_INTERVAL=1.0       # 分页延时1秒
```

## 命令行参数优化

### 快速采集示例
```bash
# 使用快速模式
python fengzs_collector.py --channel-interval 0.5 --page-interval 0.3

# 或者使用run_collector.sh
bash run_collector.sh --channel-interval 0.5 --page-interval 0.3
```

### 详细日志模式（调试用）
```bash
# 启用详细日志查看性能
python fengzs_collector.py --verbose --channel-interval 0.5 --page-interval 0.3
```

## 性能监控

### 关键指标
1. **总耗时**: 查看日志中的"耗时：XX秒"
2. **处理速度**: 订单数量/总耗时
3. **API响应时间**: 在详细日志中查看
4. **入库速度**: 批量处理的时间

### 日志分析
```bash
# 查看最新的采集日志
tail -f logs/collector_$(ls logs/collector_*.log | tail -1 | cut -d'/' -f2)

# 统计处理速度
grep "数据情况" logs/collector_*.log | tail -5
```

## 进一步优化建议

### 1. 并发处理（未实现）
如果需要更高性能，可以考虑：
- 多线程处理不同渠道
- 异步IO处理API请求
- 批量API调用

### 2. 缓存优化
- 缓存渠道配置信息
- 缓存重复的API响应

### 3. 网络优化
- 使用连接池
- 启用HTTP/2
- 压缩请求数据

## 注意事项

### 1. 接口限制
- 不要将延时设置得过低，避免触发接口限制
- 监控API响应状态，如果出现429错误需要增加延时

### 2. 服务器负载
- 监控目标服务器的响应时间
- 如果响应变慢，适当增加延时

### 3. 数据完整性
- 确保在提高速度的同时不丢失数据
- 定期验证数据采集的完整性

## 性能测试结果

### 优化前后对比
| 项目 | 优化前 | 优化后 | 提升 |
|------|--------|--------|------|
| 批量大小 | 20条 | 50条 | +150% |
| 错误重试延时 | 2-3秒 | 1-2秒 | -50% |
| 时间区间延时 | 5秒 | 2秒 | -60% |
| 分页延时优化 | 每页都延时 | 第一页不延时 | 减少首页延时 |

### 预期性能提升
- **整体速度**: 预计提升30-50%
- **入库效率**: 批量大小增加，提升150%
- **错误恢复**: 重试延时减少，提升50%
